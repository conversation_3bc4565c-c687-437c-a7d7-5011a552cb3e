<!DOCTYPE html>
<html lang="ko">
<head>
    <meta charset="utf-8">
    <meta name="viewport" content="width=device-width, initial-scale=1">
    <title>멜론 차트 TOP 100 - 멜로디 쉐어</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet"
        integrity="sha384-9ndCyUaIbzAi2FUVXJi0CjmCapSmO7SnpJef0486qhLnuZ2cdeRhO02iuK6FUUVM" crossorigin="anonymous">
    <style>
        @import url("https://cdn.jsdelivr.net/npm/bootstrap-icons@1.10.5/font/bootstrap-icons.css");

        .background-banner {
            background-image: linear-gradient(45deg,
                    rgb(51 43 43 / 75%),
                    rgb(20 19 20 / 61%)), url("https://s3.ap-northeast-2.amazonaws.com/materials.spartacodingclub.kr/webjong/images/music_festival.jpg");
            min-height: 100vh;
            background-position: center;
            background-size: cover;
            background-repeat: no-repeat;
            background-attachment: fixed;
        }
    </style>
    <link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/bootstrap-icons@1.10.5/font/bootstrap-icons.css">
</head>

<body data-bs-theme="dark">
    <div class="background-banner">
        <nav class="navbar border-bottom border-bottom-dark d-flex justify-content-space-between" data-bs-theme="dark">
            <div class="ms-3">
                <img src="https://s3.ap-northeast-2.amazonaws.com/materials.spartacodingclub.kr/webjong/images/sparta-logo.svg" alt="">
            </div>
            <nav class="navbar navbar-expand-lg">
                <div class="container-fluid">
                    <button class="navbar-toggler" type="button" data-bs-toggle="collapse" data-bs-target="#navbarNav"
                        aria-controls="navbarNav" aria-expanded="false" aria-label="Toggle navigation">
                        <span class="navbar-toggler-icon"></span>
                    </button>
                    <div class="collapse navbar-collapse" id="navbarNav">
                        <ul class="navbar-nav">
                            <li class="nav-item">
                                <a class="nav-link text-white active" aria-current="page" href="/">🎵 멜론차트</a>
                            </li>
                            <li class="nav-item">
                                <a class="nav-link text-white" href="/lotto">🎰 로또번호</a>
                            </li>
                            <li class="nav-item">
                                <a class="nav-link text-white" href="/movie">🎬 영화검색</a>
                            </li>
                        </ul>
                    </div>
                </div>
            </nav>
        </nav>

        <div class="px-4 py-5 my-5 text-center">
            <h1 class="display-5 fw-bold text-body-emphasis">🎵 멜론 차트 TOP 100</h1>
            <div class="col-lg-6 mx-auto">
                <p class="lead mb-4">
                    <br>
                    실시간 멜론 차트를 확인하세요!
                    <br><br>
                    최신 인기곡들을 한눈에 볼 수 있습니다.
                    <br>
                    음악 트렌드를 놓치지 마세요.
                </p>
                <div class="d-grid gap-2 d-sm-flex justify-content-sm-center">
                    <button type="button" class="btn btn-danger" onclick="refreshChart()">
                        🔄 차트 새로고침
                    </button>
                    <button type="button" class="btn btn-success" data-bs-toggle="modal" data-bs-target="#searchModal">
                        🔍 음악 검색
                    </button>
                </div>
                <div class="mt-3 text-center">
                    <small class="text-muted">총 {{ data|length }}곡의 차트 데이터</small>
                </div>
            </div>
        </div>

        <!-- 음악 검색 모달 -->
        <div class="modal fade" id="searchModal" tabindex="-1" aria-labelledby="searchModalLabel" aria-hidden="true">
            <div class="modal-dialog">
                <div class="modal-content">
                    <div class="modal-header">
                        <h1 class="modal-title fs-5" id="searchModalLabel">🔍 음악 검색</h1>
                        <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
                    </div>
                    <div class="modal-body">
                        <form>
                            <div class="mb-3">
                                <label for="searchQuery" class="form-label">검색어</label>
                                <input type="text" class="form-control" id="searchQuery" placeholder="아티스트명 또는 곡명을 입력하세요">
                                <div class="form-text">검색하고 싶은 아티스트명이나 곡명을 입력해주세요.</div>
                            </div>
                            <div class="mb-3">
                                <label for="searchType" class="form-label">검색 유형</label>
                                <select class="form-select" id="searchType">
                                    <option value="song">곡명</option>
                                    <option value="artist">아티스트</option>
                                    <option value="album">앨범</option>
                                </select>
                            </div>
                            <button type="button" class="btn btn-success" onclick="searchMusic()">
                                <i class="bi bi-search"></i> 멜론에서 검색
                            </button>
                        </form>
                    </div>
                </div>
            </div>
        </div>

        <!-- 차트 카드 -->
        {% if data %}
        <div class="row row-cols-1 row-cols-sm-2 row-cols-md-3 row-cols-lg-4 row-cols-xl-5 g-4 mx-auto w-90 pb-5">
            {% for song in data %}
            <div class="col">
                <div class="card h-100">
                    <div class="position-relative">
                        {% if song.img_url %}
                        <img src="{{ song.img_url }}" class="card-img-top" alt="{{ song.title }}" style="height: 200px; object-fit: cover;">
                        {% else %}
                        <div class="card-img-top bg-secondary d-flex align-items-center justify-content-center" style="height: 200px;">
                            <i class="bi bi-music-note-beamed text-white" style="font-size: 2.5rem;"></i>
                        </div>
                        {% endif %}
                        <span class="position-absolute top-0 start-0 badge bg-danger m-2" style="font-size: 0.9rem;">
                            #{{ song.rank }}
                        </span>
                    </div>
                    <div class="card-body d-flex flex-column p-3">
                        <h6 class="card-title text-truncate mb-2" title="{{ song.title }}" style="font-size: 0.9rem;">
                            {{ song.title }}
                        </h6>
                        <p class="card-text text-muted mb-1 small">
                            <i class="bi bi-person-fill"></i> {{ song.artist }}
                        </p>
                        <p class="card-text text-muted small mb-2" style="font-size: 0.75rem;">
                            <i class="bi bi-disc"></i> {{ song.album }}
                        </p>
                        <div class="mt-auto">
                            <button class="btn btn-outline-success btn-sm w-100" style="font-size: 0.75rem; padding: 0.25rem 0.5rem;"
                                    onclick="searchOnMelon('{{ song.title }}', '{{ song.artist }}')">
                                <i class="bi bi-search"></i> 멜론에서 찾기
                            </button>
                        </div>
                    </div>
                </div>
            </div>
            {% endfor %}
        </div>
        {% else %}
        <div class="text-center p-5">
            <div class="alert alert-warning" role="alert">
                <h4 class="alert-heading">데이터를 불러올 수 없습니다</h4>
                <p>차트 데이터를 가져오는 중 문제가 발생했습니다. 잠시 후 다시 시도해주세요.</p>
                <hr>
                <button class="btn btn-warning" onclick="refreshChart()">다시 시도</button>
            </div>
        </div>
        {% endif %}

        <!-- 푸터 -->
        <div class="container">
            <footer>
                <div class="d-flex flex-column flex-sm-row justify-content-between py-4 my-4 border-top">
                    <p>© 2025 멜로디 쉐어. All rights reserved.</p>
                    <ul class="list-unstyled d-flex">
                        <li class="ms-3"><a class="link-body-emphasis" href="https://www.youtube.com/@SpartaCodingClub"><i class="bi bi-youtube"></i></a></li>
                        <li class="ms-3"><a class="link-body-emphasis" href="https://www.instagram.com/spartacodingclub/"><i class="bi bi-instagram"></i></a></li>
                        <li class="ms-3"><a class="link-body-emphasis" href="https://spartacodingclub.kr/blog"><i class="bi bi-postcard"></i></a></li>
                    </ul>
                </div>
            </footer>
        </div>
    </div>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"
        integrity="sha384-geWF76RCwLtnZ8qwWowPQNguL3RmwHVBC9FhGdlKrxdiJJigb/j/68SIy3Te4Bkz"
        crossorigin="anonymous"></script>

    <script>
        function refreshChart() {
            // 로딩 표시
            const refreshBtn = document.querySelector('button[onclick="refreshChart()"]');
            if (refreshBtn) {
                const originalText = refreshBtn.innerHTML;
                refreshBtn.innerHTML = '<span class="spinner-border spinner-border-sm" role="status" aria-hidden="true"></span> 로딩중...';
                refreshBtn.disabled = true;
            }

            // 페이지 새로고침
            setTimeout(() => {
                location.reload();
            }, 1000);
        }

        function searchMusic() {
            const query = document.getElementById('searchQuery').value.trim();
            const searchType = document.getElementById('searchType').value;

            if (!query) {
                alert('검색어를 입력해주세요.');
                return;
            }

            let melonSearchUrl = '';
            switch(searchType) {
                case 'song':
                    melonSearchUrl = `https://www.melon.com/search/song/index.htm?q=${encodeURIComponent(query)}`;
                    break;
                case 'artist':
                    melonSearchUrl = `https://www.melon.com/search/artist/index.htm?q=${encodeURIComponent(query)}`;
                    break;
                case 'album':
                    melonSearchUrl = `https://www.melon.com/search/album/index.htm?q=${encodeURIComponent(query)}`;
                    break;
                default:
                    melonSearchUrl = `https://www.melon.com/search/total/index.htm?q=${encodeURIComponent(query)}`;
            }

            window.open(melonSearchUrl, '_blank');

            // 모달 닫기
            const modal = bootstrap.Modal.getInstance(document.getElementById('searchModal'));
            if (modal) {
                modal.hide();
            }
        }

        function searchOnMelon(title, artist) {
            try {
                const searchQuery = `${title} ${artist}`;
                const melonSearchUrl = `https://www.melon.com/search/song/index.htm?q=${encodeURIComponent(searchQuery)}`;
                window.open(melonSearchUrl, '_blank');
            } catch (error) {
                console.error('멜론 검색 중 오류:', error);
                alert('검색 중 오류가 발생했습니다. 다시 시도해주세요.');
            }
        }

        // Enter 키로 검색
        document.addEventListener('DOMContentLoaded', function() {
            const searchInput = document.getElementById('searchQuery');
            if (searchInput) {
                searchInput.addEventListener('keypress', function(e) {
                    if (e.key === 'Enter') {
                        e.preventDefault();
                        searchMusic();
                    }
                });
            }
        });
    </script>
</body>
</html>
