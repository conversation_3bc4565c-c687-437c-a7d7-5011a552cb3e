#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
로또번호 추천 시스템
1부터 45까지의 숫자 중에서 중복되지 않는 6개의 번호를 추천합니다.
"""

import random
import json
from datetime import datetime
from collections import Counter
import argparse

class LottoRecommender:
    """로또번호 추천 클래스"""
    
    def __init__(self):
        self.min_number = 1
        self.max_number = 45
        self.numbers_count = 6
    
    def generate_random_numbers(self):
        """완전 랜덤한 로또번호 생성"""
        numbers = random.sample(range(self.min_number, self.max_number + 1), self.numbers_count)
        return sorted(numbers)
    
    def generate_weighted_numbers(self):
        """가중치를 적용한 로또번호 생성 (일부 번호에 더 높은 확률)"""
        # 1-10: 높은 가중치, 11-30: 보통 가중치, 31-45: 낮은 가중치
        weights = []
        for i in range(1, 46):
            if i <= 10:
                weights.append(3)  # 높은 가중치
            elif i <= 30:
                weights.append(2)  # 보통 가중치
            else:
                weights.append(1)  # 낮은 가중치
        
        numbers = random.choices(range(1, 46), weights=weights, k=self.numbers_count)
        # 중복 제거 및 정렬
        numbers = list(set(numbers))
        while len(numbers) < self.numbers_count:
            new_number = random.choices(range(1, 46), weights=weights, k=1)[0]
            if new_number not in numbers:
                numbers.append(new_number)
        
        return sorted(numbers)
    
    def generate_date_based_numbers(self, date_str=None):
        """날짜 기반 로또번호 생성"""
        if date_str is None:
            date_str = datetime.now().strftime("%Y%m%d")
        
        # 날짜를 시드로 사용
        random.seed(date_str)
        numbers = self.generate_random_numbers()
        random.seed()  # 시드 초기화
        
        return numbers
    
    def generate_pattern_numbers(self):
        """패턴 기반 로또번호 생성 (연속번호, 끝자리 등 고려)"""
        numbers = []
        
        # 연속번호 1-2개 포함
        consecutive_start = random.randint(1, 44)
        numbers.extend([consecutive_start, consecutive_start + 1])
        
        # 끝자리가 같은 번호 1개 추가
        last_digit = random.randint(0, 9)
        same_ending = [n for n in range(1, 46) if n % 10 == last_digit and n not in numbers]
        if same_ending:
            numbers.append(random.choice(same_ending))
        
        # 나머지 번호 랜덤 추가
        remaining = [n for n in range(1, 46) if n not in numbers]
        numbers.extend(random.sample(remaining, self.numbers_count - len(numbers)))
        
        return sorted(numbers[:self.numbers_count])
    
    def generate_multiple_sets(self, count=5, method='random'):
        """여러 세트의 로또번호 생성"""
        methods = {
            'random': self.generate_random_numbers,
            'weighted': self.generate_weighted_numbers,
            'date': self.generate_date_based_numbers,
            'pattern': self.generate_pattern_numbers
        }
        
        if method not in methods:
            method = 'random'
        
        sets = []
        for i in range(count):
            numbers = methods[method]()
            sets.append({
                'set_number': i + 1,
                'numbers': numbers,
                'method': method,
                'sum': sum(numbers),
                'generated_at': datetime.now().isoformat()
            })
        
        return sets
    
    def analyze_numbers(self, numbers):
        """번호 분석 정보 제공"""
        analysis = {
            'numbers': numbers,
            'sum': sum(numbers),
            'average': sum(numbers) / len(numbers),
            'odd_count': len([n for n in numbers if n % 2 == 1]),
            'even_count': len([n for n in numbers if n % 2 == 0]),
            'low_count': len([n for n in numbers if n <= 22]),  # 1-22
            'high_count': len([n for n in numbers if n > 22]),   # 23-45
            'consecutive_pairs': self._find_consecutive_pairs(numbers),
            'last_digits': [n % 10 for n in numbers]
        }
        
        return analysis
    
    def _find_consecutive_pairs(self, numbers):
        """연속된 번호 쌍 찾기"""
        consecutive = []
        for i in range(len(numbers) - 1):
            if numbers[i + 1] - numbers[i] == 1:
                consecutive.append((numbers[i], numbers[i + 1]))
        return consecutive

def main():
    """메인 함수"""
    parser = argparse.ArgumentParser(description='로또번호 추천 시스템')
    parser.add_argument('--count', '-c', type=int, default=1, help='생성할 세트 수 (기본값: 1)')
    parser.add_argument('--method', '-m', choices=['random', 'weighted', 'date', 'pattern'], 
                       default='random', help='생성 방법 (기본값: random)')
    parser.add_argument('--analyze', '-a', action='store_true', help='번호 분석 정보 표시')
    parser.add_argument('--json', '-j', action='store_true', help='JSON 형식으로 출력')
    
    args = parser.parse_args()
    
    recommender = LottoRecommender()
    
    if args.count == 1:
        # 단일 세트 생성
        methods = {
            'random': recommender.generate_random_numbers,
            'weighted': recommender.generate_weighted_numbers,
            'date': recommender.generate_date_based_numbers,
            'pattern': recommender.generate_pattern_numbers
        }
        
        numbers = methods[args.method]()
        
        if args.json:
            result = {
                'numbers': numbers,
                'method': args.method,
                'generated_at': datetime.now().isoformat()
            }
            if args.analyze:
                result['analysis'] = recommender.analyze_numbers(numbers)
            print(json.dumps(result, ensure_ascii=False, indent=2))
        else:
            print(f"\n🎰 로또번호 추천 ({args.method} 방식)")
            print("=" * 40)
            print(f"추천 번호: {' - '.join(map(str, numbers))}")
            print(f"생성 시간: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
            
            if args.analyze:
                analysis = recommender.analyze_numbers(numbers)
                print(f"\n📊 번호 분석")
                print("-" * 20)
                print(f"합계: {analysis['sum']}")
                print(f"평균: {analysis['average']:.1f}")
                print(f"홀수: {analysis['odd_count']}개, 짝수: {analysis['even_count']}개")
                print(f"저번호(1-22): {analysis['low_count']}개, 고번호(23-45): {analysis['high_count']}개")
                if analysis['consecutive_pairs']:
                    print(f"연속번호: {analysis['consecutive_pairs']}")
                print(f"끝자리: {analysis['last_digits']}")
    else:
        # 여러 세트 생성
        sets = recommender.generate_multiple_sets(args.count, args.method)
        
        if args.json:
            print(json.dumps(sets, ensure_ascii=False, indent=2))
        else:
            print(f"\n🎰 로또번호 추천 ({args.count}세트, {args.method} 방식)")
            print("=" * 50)
            for set_data in sets:
                numbers = set_data['numbers']
                print(f"세트 {set_data['set_number']}: {' - '.join(map(str, numbers))} (합계: {set_data['sum']})")
                
                if args.analyze:
                    analysis = recommender.analyze_numbers(numbers)
                    print(f"  분석: 홀수 {analysis['odd_count']}, 짝수 {analysis['even_count']}, "
                          f"저번호 {analysis['low_count']}, 고번호 {analysis['high_count']}")
            
            print(f"\n생성 시간: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")

if __name__ == "__main__":
    main()
