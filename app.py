from flask import Flask, render_template, jsonify, request
import random
import requests
from datetime import datetime, timedelta
from bs4 import BeautifulSoup

app = Flask(__name__)

# 영화진흥위원회 API 키 (실제 사용시에는 환경변수나 설정파일에서 관리)
# KOBIS_API_KEY = "f5eef3421c602c6cb7ea224104795888"  # 예시 키, 실제 키로 교체 필요
KOBIS_API_KEY = "b9c21d064030d9fbbe310a41aed230bb" # 개인학습용으로 발급받은키 입니다"
KOBIS_BASE_URL = "http://www.kobis.or.kr/kobisopenapi/webservice/rest"

def scrape_melon_chart():
    """멜론 차트 TOP 100 스크래핑"""
    url = "https://www.melon.com/chart/index.htm"
    headers = {
        'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36'
    }

    try:
        response = requests.get(url, headers=headers, timeout=10)
        response.raise_for_status()
        soup = BeautifulSoup(response.text, 'html.parser')

        songs = []
        # 여러 셀렉터 시도
        song_list = soup.select('tr[data-song-no]') or soup.select('.lst50, .lst100 tr') or soup.select('#frm table tbody tr')

        if not song_list:
            print("멜론 차트 목록을 찾을 수 없습니다. 더미 데이터를 사용합니다.")
            return get_dummy_melon_chart()

        print(f"총 {len(song_list)}개의 곡을 찾았습니다.")

        for i, song in enumerate(song_list[:100], 1):  # TOP 100 가져오기
            try:
                # 순위
                rank = i

                # 곡명 - 여러 셀렉터 시도
                title_elem = song.select_one('.ellipsis.rank01 a') or song.select_one('.wrap_song_info .rank01 a') or song.select_one('.song_name a')
                title = title_elem.text.strip() if title_elem else "제목 없음"

                # 아티스트 - 여러 셀렉터 시도
                artist_elem = song.select_one('.ellipsis.rank02 a') or song.select_one('.wrap_song_info .rank02 a') or song.select_one('.artist_name a')
                artist = artist_elem.text.strip() if artist_elem else "아티스트 없음"

                # 앨범 - 여러 셀렉터 시도
                album_elem = song.select_one('.ellipsis.rank03 a') or song.select_one('.wrap_song_info .rank03 a') or song.select_one('.album_name a')
                album = album_elem.text.strip() if album_elem else "앨범 없음"

                # 앨범 커버 이미지 - 더 정확한 셀렉터
                img_elem = song.select_one('.image_typeAll img') or song.select_one('img')
                img_url = ''
                if img_elem:
                    img_url = img_elem.get('src', '') or img_elem.get('data-src', '')
                    # 상대 경로를 절대 경로로 변환
                    if img_url and img_url.startswith('//'):
                        img_url = 'https:' + img_url
                    elif img_url and img_url.startswith('/'):
                        img_url = 'https://cdnimg.melon.co.kr' + img_url

                songs.append({
                    'rank': rank,
                    'title': title,
                    'artist': artist,
                    'album': album,
                    'img_url': img_url
                })

            except Exception as e:
                print(f"곡 {i} 정보 파싱 오류: {e}")
                continue

        if not songs:
            print("스크래핑된 곡이 없습니다. 더미 데이터를 사용합니다.")
            return get_dummy_melon_chart()

        print(f"성공적으로 {len(songs)}개의 곡을 스크래핑했습니다.")
        return songs

    except requests.exceptions.Timeout:
        print("멜론 사이트 연결 시간 초과. 더미 데이터를 사용합니다.")
        return get_dummy_melon_chart()
    except requests.exceptions.RequestException as e:
        print(f"멜론 사이트 연결 오류: {e}. 더미 데이터를 사용합니다.")
        return get_dummy_melon_chart()
    except Exception as e:
        print(f"예상치 못한 스크래핑 오류: {e}. 더미 데이터를 사용합니다.")
        return get_dummy_melon_chart()

def get_dummy_melon_chart():
    """더미 멜론 차트 데이터 - TOP 100"""
    dummy_songs = [
        ('Supernova', 'aespa', 'Armageddon - The 1st Album'),
        ('How Sweet', 'NewJeans', 'How Sweet'),
        ('SPOT!', 'ZICO (Feat. JENNIE)', 'SPOT!'),
        ('Armageddon', 'aespa', 'Armageddon - The 1st Album'),
        ('Magnetic', 'ILLIT', 'SUPER REAL ME'),
        ('Small girl', '이영지 (feat. 도경수)', 'Small girl'),
        ('고민중독', 'QWER', '1st Mini Album \'MANITO\''),
        ('Bubble Gum', 'NewJeans', 'How Sweet'),
        ('천상연', '이창섭', '1집 Piece of BTOB Vol.1'),
        ('Sticky', 'KISS OF LIFE', 'Sticky'),
        ('클락션 (Klaxon)', '(여자)아이들', '2집 2'),
        ('나는 아픈 건 딱 질색이니까', '(여자)아이들', 'I feel'),
        ('Supernatural', 'NewJeans', 'Supernatural'),
        ('Welcome to the Show', 'DAY6', 'Fourever'),
        ('한 페이지가 될 수 있게', 'DAY6', 'The Book of Us : Gravity'),
        ('Love wins all', 'IU', 'The Winning'),
        ('Whiplash', 'aespa', 'Whiplash - The 5th Mini Album'),
        ('APT.', 'ROSÉ & Bruno Mars', 'APT.'),
        ('내 이름 맑음', 'QWER', '1st Mini Album \'MANITO\''),
        ('HAPPY', 'DAY6', 'Fourever')
    ]

    chart_data = []
    for i in range(100):
        song_data = dummy_songs[i % len(dummy_songs)]
        chart_data.append({
            'rank': i + 1,
            'title': song_data[0],
            'artist': song_data[1],
            'album': song_data[2],
            'img_url': 'https://cdnimg.melon.co.kr/cm2/album/images/default_album.jpg'
        })

    return chart_data

def get_daily_box_office(target_date=None):
    """일별 박스오피스 조회"""
    if target_date is None:
        # 어제 날짜 (박스오피스는 하루 전 데이터만 제공)
        yesterday = datetime.now() - timedelta(days=1)
        target_date = yesterday.strftime("%Y%m%d")

    url = f"{KOBIS_BASE_URL}/boxoffice/searchDailyBoxOfficeList.json"
    params = {
        'key': KOBIS_API_KEY,
        'targetDt': target_date
    }

    try:
        response = requests.get(url, params=params, timeout=10)
        response.raise_for_status()
        data = response.json()

        if 'boxOfficeResult' in data and data['boxOfficeResult']['dailyBoxOfficeList']:
            return data['boxOfficeResult']['dailyBoxOfficeList']
        else:
            # API 응답이 없거나 빈 경우 더미 데이터 반환
            return get_dummy_box_office()
    except requests.exceptions.RequestException as e:
        print(f"박스오피스 API 오류: {e}")
        return get_dummy_box_office()

def get_dummy_box_office():
    """더미 박스오피스 데이터"""
    return [
        {
            "rank": "1",
            "movieNm": "스파이더맨: 노 웨이 홈",
            "openDt": "2021-12-15",
            "audiCnt": "7500000",
            "audiInten": "150000"
        },
        {
            "rank": "2",
            "movieNm": "이터널스",
            "openDt": "2021-11-03",
            "audiCnt": "3200000",
            "audiInten": "85000"
        },
        {
            "rank": "3",
            "movieNm": "베놈 2: 렛 데어 비 카니지",
            "openDt": "2021-10-13",
            "audiCnt": "2800000",
            "audiInten": "65000"
        },
        {
            "rank": "4",
            "movieNm": "듄",
            "openDt": "2021-10-20",
            "audiCnt": "1900000",
            "audiInten": "45000"
        },
        {
            "rank": "5",
            "movieNm": "007 노 타임 투 다이",
            "openDt": "2021-09-30",
            "audiCnt": "1500000",
            "audiInten": "32000"
        }
    ]

def search_movie_info(movie_name):
    """영화 정보 검색"""
    url = f"{KOBIS_BASE_URL}/movie/searchMovieList.json"
    params = {
        'key': KOBIS_API_KEY,
        'movieNm': movie_name
    }

    try:
        response = requests.get(url, params=params, timeout=10)
        response.raise_for_status()
        data = response.json()

        if 'movieListResult' in data and data['movieListResult']['movieList']:
            return data['movieListResult']['movieList']
        else:
            # API 응답이 없거나 빈 경우 더미 데이터 반환
            return get_dummy_movie_search(movie_name)
    except requests.exceptions.RequestException as e:
        print(f"영화 검색 API 오류: {e}")
        return get_dummy_movie_search(movie_name)

def get_dummy_movie_search(movie_name):
    """더미 영화 검색 데이터"""
    # 검색어에 따른 더미 데이터
    dummy_movies = {
        "어벤져스": [
            {
                "movieCd": "20120001",
                "movieNm": "어벤져스",
                "movieNmEn": "The Avengers",
                "prdtYear": "2012",
                "openDt": "20120426",
                "typeNm": "장편",
                "prdtStatNm": "개봉",
                "nationAlt": "미국",
                "genreAlt": "액션,어드벤처,SF",
                "repGenreNm": "액션",
                "directorNm": "조스 웨던"
            },
            {
                "movieCd": "20150001",
                "movieNm": "어벤져스: 에이지 오브 울트론",
                "movieNmEn": "Avengers: Age of Ultron",
                "prdtYear": "2015",
                "openDt": "20150423",
                "typeNm": "장편",
                "prdtStatNm": "개봉",
                "nationAlt": "미국",
                "genreAlt": "액션,어드벤처,SF",
                "repGenreNm": "액션",
                "directorNm": "조스 웨던"
            }
        ],
        "스파이더맨": [
            {
                "movieCd": "20210001",
                "movieNm": "스파이더맨: 노 웨이 홈",
                "movieNmEn": "Spider-Man: No Way Home",
                "prdtYear": "2021",
                "openDt": "20211215",
                "typeNm": "장편",
                "prdtStatNm": "개봉",
                "nationAlt": "미국",
                "genreAlt": "액션,어드벤처,SF",
                "repGenreNm": "액션",
                "directorNm": "존 왓츠"
            }
        ],
        "기생충": [
            {
                "movieCd": "20190001",
                "movieNm": "기생충",
                "movieNmEn": "Parasite",
                "prdtYear": "2019",
                "openDt": "20190530",
                "typeNm": "장편",
                "prdtStatNm": "개봉",
                "nationAlt": "한국",
                "genreAlt": "드라마,스릴러",
                "repGenreNm": "드라마",
                "directorNm": "봉준호"
            }
        ]
    }

    # 검색어와 일치하는 더미 데이터 찾기
    for key, movies in dummy_movies.items():
        if key in movie_name or movie_name in key:
            return movies

    # 일치하는 것이 없으면 일반적인 더미 데이터 반환
    return [
        {
            "movieCd": "20230001",
            "movieNm": f"{movie_name} (검색 결과)",
            "movieNmEn": f"{movie_name} (Search Result)",
            "prdtYear": "2023",
            "openDt": "20230101",
            "typeNm": "장편",
            "prdtStatNm": "개봉",
            "nationAlt": "한국",
            "genreAlt": "드라마",
            "repGenreNm": "드라마",
            "directorNm": "감독명"
        }
    ]

def get_movie_detail(movie_cd):
    """영화 상세 정보 조회"""
    url = f"{KOBIS_BASE_URL}/movie/searchMovieInfo.json"
    params = {
        'key': KOBIS_API_KEY,
        'movieCd': movie_cd
    }

    try:
        response = requests.get(url, params=params, timeout=10)
        response.raise_for_status()
        data = response.json()

        if 'movieInfoResult' in data:
            return data['movieInfoResult']['movieInfo']
        return {}
    except requests.exceptions.RequestException as e:
        print(f"영화 상세정보 API 오류: {e}")
        return {}

def generate_lotto_numbers():
    """1부터 45까지의 숫자 중에서 중복되지 않는 6개의 번호를 생성"""
    numbers = random.sample(range(1, 46), 6)
    return sorted(numbers)

def generate_multiple_lotto_sets(count=5):
    """여러 세트의 로또번호를 생성"""
    lotto_sets = []
    for i in range(count):
        numbers = generate_lotto_numbers()
        lotto_sets.append({
            'set_number': i + 1,
            'numbers': numbers
        })
    return lotto_sets

def get_lucky_numbers_by_date():
    """오늘 날짜를 기반으로 한 행운의 번호 생성"""
    today = datetime.now()
    # 날짜를 시드로 사용하여 일관된 결과 생성
    random.seed(today.strftime("%Y%m%d"))
    lucky_numbers = generate_lotto_numbers()
    # 시드 초기화
    random.seed()
    return lucky_numbers

@app.route('/')
def home():
    """메인 페이지 - 멜론 차트 TOP 100"""
    # 멜론 차트 데이터 스크래핑
    chart_data = scrape_melon_chart()
    return render_template('melon.html', data=chart_data)

@app.route('/lotto')
def lotto_page():
    """로또번호 추천 페이지"""
    name = '김태민'
    # 추천 로또번호 생성
    recommended_numbers = generate_lotto_numbers()
    # 오늘의 행운 번호
    lucky_numbers = get_lucky_numbers_by_date()
    # 여러 세트 생성
    multiple_sets = generate_multiple_lotto_sets(3)

    context = {
        "name": name,
        "lotto": recommended_numbers,
        "lucky_numbers": lucky_numbers,
        "multiple_sets": multiple_sets,
        "generation_time": datetime.now().strftime("%Y-%m-%d %H:%M:%S")
    }
    return render_template('index.html', data=context)
@app.route('/mypage')
def mypage():
    return 'This is MyPage!'

@app.route('/api/lotto')
def api_lotto():
    """로또번호 API 엔드포인트"""
    return jsonify({
        'numbers': generate_lotto_numbers(),
        'generated_at': datetime.now().isoformat()
    })

@app.route('/api/lotto/multiple/<int:count>')
def api_multiple_lotto(count):
    """여러 세트의 로또번호 API 엔드포인트"""
    if count > 10:  # 최대 10세트로 제한
        count = 10
    if count < 1:
        count = 1

    return jsonify({
        'sets': generate_multiple_lotto_sets(count),
        'generated_at': datetime.now().isoformat()
    })

@app.route('/api/lotto/lucky')
def api_lucky_lotto():
    """오늘의 행운 번호 API 엔드포인트"""
    return jsonify({
        'lucky_numbers': get_lucky_numbers_by_date(),
        'date': datetime.now().strftime("%Y-%m-%d"),
        'generated_at': datetime.now().isoformat()
    })

@app.route('/movie')
def movie_page():
    """영화 검색 페이지"""
    search_query = request.args.get('search', '')
    movies = []
    box_office = []
    error_message = None

    # 박스오피스 정보 가져오기
    try:
        box_office = get_daily_box_office()
    except Exception as e:
        error_message = f"박스오피스 정보를 가져올 수 없습니다: {str(e)}"

    # 검색어가 있으면 영화 검색
    if search_query:
        try:
            movies = search_movie_info(search_query)
        except Exception as e:
            error_message = f"영화 검색 중 오류가 발생했습니다: {str(e)}"

    context = {
        'search_query': search_query,
        'movies': movies,
        'box_office': box_office,
        'error_message': error_message,
        'search_time': datetime.now().strftime("%Y-%m-%d %H:%M:%S")
    }

    return render_template('movie.html', data=context)



@app.route('/api/melon/chart')
def api_melon_chart():
    """멜론 차트 API 엔드포인트"""
    chart_data = scrape_melon_chart()
    return jsonify({
        'chart': chart_data,
        'total_count': len(chart_data),
        'retrieved_at': datetime.now().isoformat()
    })

@app.route('/api/movie/search')
def api_movie_search():
    """영화 검색 API"""
    movie_name = request.args.get('q', '')
    if not movie_name:
        return jsonify({'error': '검색어를 입력해주세요'}), 400

    try:
        movies = search_movie_info(movie_name)
        return jsonify({
            'query': movie_name,
            'results': movies,
            'count': len(movies),
            'searched_at': datetime.now().isoformat()
        })
    except Exception as e:
        return jsonify({'error': str(e)}), 500

@app.route('/api/movie/boxoffice')
def api_box_office():
    """박스오피스 API"""
    target_date = request.args.get('date')

    try:
        box_office = get_daily_box_office(target_date)
        return jsonify({
            'date': target_date or (datetime.now() - timedelta(days=1)).strftime("%Y%m%d"),
            'box_office': box_office,
            'count': len(box_office),
            'retrieved_at': datetime.now().isoformat()
        })
    except Exception as e:
        return jsonify({'error': str(e)}), 500

@app.route('/api/movie/detail/<movie_cd>')
def api_movie_detail(movie_cd):
    """영화 상세정보 API"""
    try:
        movie_detail = get_movie_detail(movie_cd)
        return jsonify({
            'movie_code': movie_cd,
            'movie_info': movie_detail,
            'retrieved_at': datetime.now().isoformat()
        })
    except Exception as e:
        return jsonify({'error': str(e)}), 500

if __name__ == '__main__':  
    app.run(debug=True)