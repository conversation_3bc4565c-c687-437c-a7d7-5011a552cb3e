<!DOCTYPE html>
<html lang="ko">
<head>
    <meta charset="utf-8">
    <meta name="viewport" content="width=device-width, initial-scale=1">
    <title>내 음악 - 멜로디 쉐어</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet"
        integrity="sha384-9ndCyUaIbzAi2FUVXJi0CjmCapSmO7SnpJef0486qhLnuZ2cdeRhO02iuK6FUUVM" crossorigin="anonymous">
    <style>
        @import url("https://cdn.jsdelivr.net/npm/bootstrap-icons@1.10.5/font/bootstrap-icons.css");

        .background-banner {
            background-image: linear-gradient(45deg,
                    rgb(51 43 43 / 75%),
                    rgb(20 19 20 / 61%)), url("https://s3.ap-northeast-2.amazonaws.com/materials.spartacodingclub.kr/webjong/images/music_festival.jpg");
            min-height: 100vh;
            background-position: center;
            background-size: cover;
            background-repeat: no-repeat;
            background-attachment: fixed;
        }
    </style>
    <link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/bootstrap-icons@1.10.5/font/bootstrap-icons.css">
</head>

<body data-bs-theme="dark">
    <div class="background-banner">
        <nav class="navbar border-bottom border-bottom-dark d-flex justify-content-space-between" data-bs-theme="dark">
            <div class="ms-3">
                <img src="https://s3.ap-northeast-2.amazonaws.com/materials.spartacodingclub.kr/webjong/images/sparta-logo.svg" alt="">
            </div>
            <nav class="navbar navbar-expand-lg">
                <div class="container-fluid">
                    <button class="navbar-toggler" type="button" data-bs-toggle="collapse" data-bs-target="#navbarNav"
                        aria-controls="navbarNav" aria-expanded="false" aria-label="Toggle navigation">
                        <span class="navbar-toggler-icon"></span>
                    </button>
                    <div class="collapse navbar-collapse" id="navbarNav">
                        <ul class="navbar-nav">
                            <li class="nav-item">
                                <a class="nav-link text-white" href="/">🎵 멜론차트</a>
                            </li>
                            <li class="nav-item">
                                <a class="nav-link text-white active" aria-current="page" href="/mymusic">🎶 내음악</a>
                            </li>
                            <li class="nav-item">
                                <a class="nav-link text-white" href="/lotto">🎰 로또번호</a>
                            </li>
                            <li class="nav-item">
                                <a class="nav-link text-white" href="/movie">🎬 영화검색</a>
                            </li>
                        </ul>
                    </div>
                </div>
            </nav>
        </nav>

        <div class="px-4 py-5 my-5 text-center">
            <h1 class="display-5 fw-bold text-body-emphasis">🎶 내 음악 목록</h1>
            <div class="col-lg-6 mx-auto">
                <p class="lead mb-4">
                    <br>
                    멜론 차트에서 추가한 내 음악들을 확인하세요!
                    <br><br>
                    좋아하는 음악들을 모아서 관리할 수 있습니다.
                    <br>
                </p>
                <div class="d-grid gap-2 d-sm-flex justify-content-sm-center">
                    <button type="button" class="btn btn-primary" onclick="location.href='/'">
                        🎵 멜론차트로 돌아가기
                    </button>
                    <button type="button" class="btn btn-danger" onclick="clearAllMusic()">
                        🗑️ 전체 삭제
                    </button>
                </div>
                <div class="mt-3 text-center">
                    <small class="text-muted">총 {{ data|length }}곡의 음악</small>
                </div>
            </div>
        </div>

        <!-- 음악 목록 -->
        {% if data %}
        <div class="row row-cols-1 row-cols-sm-2 row-cols-md-3 row-cols-lg-4 row-cols-xl-5 g-4 mx-auto w-90 pb-5">
            {% for song in data %}
            <div class="col">
                <div class="card h-100">
                    <div class="position-relative">
                        {% if song.img_url %}
                        <img src="{{ song.img_url }}" class="card-img-top" alt="{{ song.title }}" style="height: 200px; object-fit: cover;">
                        {% else %}
                        <div class="card-img-top bg-secondary d-flex align-items-center justify-content-center" style="height: 200px;">
                            <i class="bi bi-music-note-beamed text-white" style="font-size: 2.5rem;"></i>
                        </div>
                        {% endif %}
                        <span class="position-absolute top-0 start-0 badge bg-success m-2" style="font-size: 0.9rem;">
                            #{{ song.rank }}
                        </span>
                        <span class="position-absolute top-0 end-0 badge bg-info m-2" style="font-size: 0.7rem;">
                            {{ song.added_at[:10] if song.added_at else '' }}
                        </span>
                    </div>
                    <div class="card-body d-flex flex-column p-3">
                        <h6 class="card-title text-truncate mb-2" title="{{ song.title }}" style="font-size: 0.9rem;">
                            {{ song.title }}
                        </h6>
                        <p class="card-text text-muted mb-1 small">
                            <i class="bi bi-person-fill"></i> {{ song.artist }}
                        </p>
                        <p class="card-text text-muted small mb-2" style="font-size: 0.75rem;">
                            <i class="bi bi-disc"></i> {{ song.album }}
                        </p>
                        <div class="mt-auto">
                            <div class="d-grid gap-1">
                                <button class="btn btn-outline-success btn-sm" style="font-size: 0.75rem; padding: 0.25rem 0.5rem;"
                                        onclick="searchOnMelon('{{ song.title }}', '{{ song.artist }}')">
                                    <i class="bi bi-search"></i> 멜론에서 찾기
                                </button>
                                <button class="btn btn-outline-danger btn-sm" style="font-size: 0.75rem; padding: 0.25rem 0.5rem;"
                                        onclick="removeFromMyMusic('{{ song.title }}', '{{ song.artist }}')">
                                    <i class="bi bi-trash"></i> 목록에서 제거
                                </button>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
            {% endfor %}
        </div>
        {% else %}
        <div class="text-center p-5">
            <div class="alert alert-info" role="alert">
                <h4 class="alert-heading">아직 추가된 음악이 없습니다</h4>
                <p>멜론 차트에서 좋아하는 음악을 추가해보세요!</p>
                <hr>
                <a href="/" class="btn btn-primary">멜론 차트 보러가기</a>
            </div>
        </div>
        {% endif %}

        <!-- 푸터 -->
        <div class="container">
            <footer>
                <div class="d-flex flex-column flex-sm-row justify-content-between py-4 my-4 border-top">
                    <p>© 2025 멜로디 쉐어. All rights reserved.</p>
                    <ul class="list-unstyled d-flex">
                        <li class="ms-3"><a class="link-body-emphasis" href="https://www.youtube.com/@SpartaCodingClub"><i class="bi bi-youtube"></i></a></li>
                        <li class="ms-3"><a class="link-body-emphasis" href="https://www.instagram.com/spartacodingclub/"><i class="bi bi-instagram"></i></a></li>
                        <li class="ms-3"><a class="link-body-emphasis" href="https://spartacodingclub.kr/blog"><i class="bi bi-postcard"></i></a></li>
                    </ul>
                </div>
            </footer>
        </div>
    </div>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"
        integrity="sha384-geWF76RCwLtnZ8qwWowPQNguL3RmwHVBC9FhGdlKrxdiJJigb/j/68SIy3Te4Bkz"
        crossorigin="anonymous"></script>

    <script>
        function searchOnMelon(title, artist) {
            try {
                const searchQuery = `${title} ${artist}`;
                const melonSearchUrl = `https://www.melon.com/search/song/index.htm?q=${encodeURIComponent(searchQuery)}`;
                window.open(melonSearchUrl, '_blank');
            } catch (error) {
                console.error('멜론 검색 중 오류:', error);
                alert('검색 중 오류가 발생했습니다. 다시 시도해주세요.');
            }
        }

        function removeFromMyMusic(title, artist) {
            if (!confirm(`"${title} - ${artist}"를 목록에서 제거하시겠습니까?`)) {
                return;
            }

            // 버튼 비활성화 및 로딩 표시
            const button = event.target;
            const originalText = button.innerHTML;
            button.innerHTML = '<span class="spinner-border spinner-border-sm" role="status" aria-hidden="true"></span> 제거중...';
            button.disabled = true;

            const musicData = {
                title: title,
                artist: artist
            };

            fetch('/api/music/remove', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                },
                body: JSON.stringify(musicData)
            })
            .then(response => response.json())
            .then(data => {
                if (data.success) {
                    // 성공 시 해당 카드를 페이드아웃하고 제거
                    const card = button.closest('.col');
                    card.style.transition = 'opacity 0.5s ease-out';
                    card.style.opacity = '0';

                    setTimeout(() => {
                        card.remove();
                        // 총 개수 업데이트
                        updateMusicCount();
                        showToast('성공', data.message, 'success');
                    }, 500);
                } else {
                    // 실패 시 원래 상태로 복원
                    button.innerHTML = originalText;
                    button.disabled = false;
                    showToast('오류', data.message, 'error');
                }
            })
            .catch(error => {
                console.error('음악 제거 중 오류:', error);
                button.innerHTML = originalText;
                button.disabled = false;
                showToast('오류', '음악 제거 중 오류가 발생했습니다.', 'error');
            });
        }

        function clearAllMusic() {
            if (!confirm('모든 음악을 삭제하시겠습니까? 이 작업은 되돌릴 수 없습니다.')) {
                return;
            }

            // 버튼 비활성화 및 로딩 표시
            const button = event.target;
            const originalText = button.innerHTML;
            button.innerHTML = '<span class="spinner-border spinner-border-sm" role="status" aria-hidden="true"></span> 삭제중...';
            button.disabled = true;

            fetch('/api/music/clear', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                }
            })
            .then(response => response.json())
            .then(data => {
                if (data.success) {
                    // 성공 시 페이지 새로고침
                    showToast('성공', data.message, 'success');
                    setTimeout(() => {
                        location.reload();
                    }, 1500);
                } else {
                    // 실패 시 원래 상태로 복원
                    button.innerHTML = originalText;
                    button.disabled = false;
                    showToast('오류', data.message, 'error');
                }
            })
            .catch(error => {
                console.error('전체 음악 삭제 중 오류:', error);
                button.innerHTML = originalText;
                button.disabled = false;
                showToast('오류', '전체 음악 삭제 중 오류가 발생했습니다.', 'error');
            });
        }

        function updateMusicCount() {
            const musicCards = document.querySelectorAll('.col:not([style*="opacity: 0"])');
            const countElement = document.querySelector('.text-muted');
            if (countElement) {
                countElement.textContent = `총 ${musicCards.length}곡의 음악`;
            }
        }

        function showToast(title, message, type) {
            // 간단한 토스트 알림 (Bootstrap 토스트 대신 alert 사용)
            const icon = type === 'success' ? '✅' : type === 'warning' ? '⚠️' : '❌';
            alert(`${icon} ${title}: ${message}`);
        }
    </script>
</body>
</html>
