<!DOCTYPE html>
<html lang="ko">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>영화 검색 시스템</title>
    <style>
        body {
            font-family: 'Arial', sans-serif;
            max-width: 1200px;
            margin: 0 auto;
            padding: 20px;
            background-color: #f5f5f5;
        }
        .container {
            background-color: white;
            padding: 30px;
            border-radius: 10px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        h1 {
            color: #2c3e50;
            text-align: center;
            margin-bottom: 30px;
        }
        .search-section {
            margin: 30px 0;
            padding: 20px;
            border: 2px solid #e74c3c;
            border-radius: 8px;
            background-color: #fdf2f2;
        }
        .search-form {
            display: flex;
            gap: 10px;
            margin-bottom: 20px;
        }
        .search-input {
            flex: 1;
            padding: 12px;
            border: 1px solid #ddd;
            border-radius: 5px;
            font-size: 16px;
        }
        .search-btn {
            padding: 12px 24px;
            background-color: #e74c3c;
            color: white;
            border: none;
            border-radius: 5px;
            cursor: pointer;
            font-size: 16px;
        }
        .search-btn:hover {
            background-color: #c0392b;
        }
        .section {
            margin: 30px 0;
            padding: 20px;
            border-radius: 8px;
            background-color: #ecf0f1;
        }
        .box-office-section {
            border: 2px solid #f39c12;
        }
        .movie-results-section {
            border: 2px solid #9b59b6;
        }
        .section-title {
            color: #2c3e50;
            margin-bottom: 15px;
            font-size: 20px;
        }
        .movie-grid {
            display: grid;
            grid-template-columns: repeat(auto-fill, minmax(300px, 1fr));
            gap: 20px;
            margin-top: 20px;
        }
        .movie-card {
            background-color: white;
            padding: 20px;
            border-radius: 8px;
            box-shadow: 0 2px 5px rgba(0,0,0,0.1);
            border-left: 4px solid #3498db;
        }
        .movie-title {
            font-size: 18px;
            font-weight: bold;
            color: #2c3e50;
            margin-bottom: 10px;
        }
        .movie-info {
            color: #7f8c8d;
            font-size: 14px;
            margin: 5px 0;
        }
        .box-office-item {
            background-color: white;
            padding: 15px;
            margin: 10px 0;
            border-radius: 5px;
            border-left: 4px solid #f39c12;
            display: flex;
            justify-content: space-between;
            align-items: center;
        }
        .rank {
            font-size: 24px;
            font-weight: bold;
            color: #f39c12;
            margin-right: 15px;
        }
        .movie-details {
            flex: 1;
        }
        .audience-count {
            color: #27ae60;
            font-weight: bold;
        }
        .error-message {
            background-color: #e74c3c;
            color: white;
            padding: 15px;
            border-radius: 5px;
            margin: 20px 0;
        }
        .no-results {
            text-align: center;
            color: #7f8c8d;
            padding: 40px;
            font-style: italic;
        }
        .timestamp {
            text-align: center;
            color: #7f8c8d;
            font-size: 14px;
            margin-top: 20px;
        }
        .nav-links {
            text-align: center;
            margin: 20px 0;
        }
        .nav-link {
            display: inline-block;
            margin: 0 10px;
            padding: 10px 20px;
            background-color: #3498db;
            color: white;
            text-decoration: none;
            border-radius: 5px;
        }
        .nav-link:hover {
            background-color: #2980b9;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>🎬 영화 검색 시스템</h1>
        
        <div class="nav-links">
            <a href="/" class="nav-link">� 멜론차트</a>
            <a href="/lotto" class="nav-link">� 로또번호</a>
            <a href="/movie" class="nav-link">� 영화검색</a>
        </div>

        <!-- 검색 섹션 -->
        <div class="search-section">
            <h3 class="section-title">🔍 영화 검색</h3>
            <form class="search-form" method="GET">
                <input type="text" name="search" class="search-input" 
                       placeholder="영화 제목을 입력하세요..." 
                       value="{{ data.search_query }}">
                <button type="submit" class="search-btn">검색</button>
            </form>
        </div>

        <!-- 오류 메시지 -->
        {% if data.error_message %}
            <div class="error-message">
                {{ data.error_message }}
            </div>
        {% endif %}

        <!-- 박스오피스 섹션 -->
        {% if data.box_office %}
            <div class="section box-office-section">
                <h3 class="section-title">🏆 일일 박스오피스 (어제 기준)</h3>
                {% for movie in data.box_office[:10] %}
                    <div class="box-office-item">
                        <div class="rank">{{ movie.rank }}</div>
                        <div class="movie-details">
                            <div class="movie-title">{{ movie.movieNm }}</div>
                            <div class="movie-info">
                                개봉일: {{ movie.openDt }} | 
                                누적관객수: {{ "{:,}".format(movie.audiCnt|int) }}명
                            </div>
                        </div>
                        <div class="audience-count">
                            {{ "{:,}".format(movie.audiInten|int) }}명
                        </div>
                    </div>
                {% endfor %}
            </div>
        {% endif %}

        <!-- 검색 결과 섹션 -->
        {% if data.search_query %}
            <div class="section movie-results-section">
                <h3 class="section-title">🎭 "{{ data.search_query }}" 검색 결과</h3>
                
                {% if data.movies %}
                    <div class="movie-grid">
                        {% for movie in data.movies %}
                            <div class="movie-card">
                                <div class="movie-title">{{ movie.movieNm }}</div>
                                <div class="movie-info">
                                    <strong>영화코드:</strong> {{ movie.movieCd }}
                                </div>
                                {% if movie.prdtYear %}
                                    <div class="movie-info">
                                        <strong>제작년도:</strong> {{ movie.prdtYear }}
                                    </div>
                                {% endif %}
                                {% if movie.openDt %}
                                    <div class="movie-info">
                                        <strong>개봉일:</strong> {{ movie.openDt }}
                                    </div>
                                {% endif %}
                                {% if movie.typeNm %}
                                    <div class="movie-info">
                                        <strong>유형:</strong> {{ movie.typeNm }}
                                    </div>
                                {% endif %}
                                {% if movie.prdtStatNm %}
                                    <div class="movie-info">
                                        <strong>제작상태:</strong> {{ movie.prdtStatNm }}
                                    </div>
                                {% endif %}
                                {% if movie.nationAlt %}
                                    <div class="movie-info">
                                        <strong>국가:</strong> {{ movie.nationAlt }}
                                    </div>
                                {% endif %}
                                {% if movie.genreAlt %}
                                    <div class="movie-info">
                                        <strong>장르:</strong> {{ movie.genreAlt }}
                                    </div>
                                {% endif %}
                                {% if movie.repGenreNm %}
                                    <div class="movie-info">
                                        <strong>대표장르:</strong> {{ movie.repGenreNm }}
                                    </div>
                                {% endif %}
                                {% if movie.directorNm %}
                                    <div class="movie-info">
                                        <strong>감독:</strong> {{ movie.directorNm }}
                                    </div>
                                {% endif %}
                            </div>
                        {% endfor %}
                    </div>
                {% else %}
                    <div class="no-results">
                        검색 결과가 없습니다. 다른 검색어를 시도해보세요.
                    </div>
                {% endif %}
            </div>
        {% endif %}

        <div class="timestamp">
            {% if data.search_time %}
                검색 시간: {{ data.search_time }}
            {% endif %}
        </div>
    </div>
</body>
</html>
