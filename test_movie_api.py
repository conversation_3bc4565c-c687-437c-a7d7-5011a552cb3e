#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
영화 API 테스트 스크립트
Flask 서버가 실행 중일 때 영화 검색 API를 테스트합니다.
"""

import requests
import json
import sys

BASE_URL = "http://127.0.0.1:5000"

def test_box_office():
    """박스오피스 API 테스트"""
    print("🏆 박스오피스 API 테스트")
    print("=" * 40)
    
    try:
        response = requests.get(f"{BASE_URL}/api/movie/boxoffice", timeout=10)
        response.raise_for_status()
        data = response.json()
        
        print(f"날짜: {data['date']}")
        print(f"영화 수: {data['count']}")
        print("\n박스오피스 순위:")
        
        for movie in data['box_office'][:5]:
            print(f"{movie['rank']}위: {movie['movieNm']} "
                  f"(관객수: {int(movie['audiCnt']):,}명)")
        
        return True
    except Exception as e:
        print(f"오류: {e}")
        return False

def test_movie_search(query):
    """영화 검색 API 테스트"""
    print(f"\n🔍 영화 검색 API 테스트: '{query}'")
    print("=" * 40)
    
    try:
        params = {'q': query}
        response = requests.get(f"{BASE_URL}/api/movie/search", 
                              params=params, timeout=10)
        response.raise_for_status()
        data = response.json()
        
        print(f"검색어: {data['query']}")
        print(f"결과 수: {data['count']}")
        
        if data['results']:
            print("\n검색 결과:")
            for i, movie in enumerate(data['results'][:3], 1):
                print(f"{i}. {movie['movieNm']}")
                if movie.get('movieNmEn'):
                    print(f"   영문명: {movie['movieNmEn']}")
                if movie.get('prdtYear'):
                    print(f"   제작년도: {movie['prdtYear']}")
                if movie.get('directorNm'):
                    print(f"   감독: {movie['directorNm']}")
                if movie.get('genreAlt'):
                    print(f"   장르: {movie['genreAlt']}")
                print()
        else:
            print("검색 결과가 없습니다.")
        
        return True
    except Exception as e:
        print(f"오류: {e}")
        return False

def test_movie_detail(movie_cd):
    """영화 상세정보 API 테스트"""
    print(f"\n📋 영화 상세정보 API 테스트: {movie_cd}")
    print("=" * 40)
    
    try:
        response = requests.get(f"{BASE_URL}/api/movie/detail/{movie_cd}", 
                              timeout=10)
        response.raise_for_status()
        data = response.json()
        
        print(f"영화 코드: {data['movie_code']}")
        
        if data['movie_info']:
            movie = data['movie_info']
            print(f"영화명: {movie.get('movieNm', 'N/A')}")
            print(f"영문명: {movie.get('movieNmEn', 'N/A')}")
            print(f"제작년도: {movie.get('prdtYear', 'N/A')}")
            print(f"개봉일: {movie.get('openDt', 'N/A')}")
            print(f"상영시간: {movie.get('showTm', 'N/A')}분")
            print(f"관람등급: {movie.get('watchGradeNm', 'N/A')}")
        else:
            print("상세정보를 찾을 수 없습니다.")
        
        return True
    except Exception as e:
        print(f"오류: {e}")
        return False

def check_server():
    """서버 상태 확인"""
    try:
        response = requests.get(f"{BASE_URL}/", timeout=5)
        return response.status_code == 200
    except:
        return False

def main():
    """메인 함수"""
    print("🎬 영화 API 테스트 시작")
    print("=" * 50)
    
    # 서버 상태 확인
    if not check_server():
        print("❌ Flask 서버가 실행되지 않았습니다.")
        print("다음 명령으로 서버를 먼저 실행해주세요:")
        print("python app.py")
        sys.exit(1)
    
    print("✅ Flask 서버 연결 확인")
    
    # 박스오피스 테스트
    success1 = test_box_office()
    
    # 영화 검색 테스트
    test_queries = ["어벤져스", "스파이더맨", "기생충", "존재하지않는영화"]
    success2 = True
    
    for query in test_queries:
        if not test_movie_search(query):
            success2 = False
    
    # 영화 상세정보 테스트
    success3 = test_movie_detail("20120001")
    
    # 결과 요약
    print("\n" + "=" * 50)
    print("📊 테스트 결과 요약")
    print("=" * 50)
    print(f"박스오피스 API: {'✅ 성공' if success1 else '❌ 실패'}")
    print(f"영화 검색 API: {'✅ 성공' if success2 else '❌ 실패'}")
    print(f"영화 상세정보 API: {'✅ 성공' if success3 else '❌ 실패'}")
    
    if success1 and success2 and success3:
        print("\n🎉 모든 테스트가 성공했습니다!")
    else:
        print("\n⚠️  일부 테스트가 실패했습니다.")

if __name__ == "__main__":
    main()
